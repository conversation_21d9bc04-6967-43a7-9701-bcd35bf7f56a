import { NextRequest, NextResponse } from 'next/server';

// Import functions directly from the application modules
async function processMainFunction(pnr: string) {
  try {
    // Import required functions
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { retrieveAndSavePNRData, fetchCoverGeniusPolicy, generateManualPolicyRequest } = require('../../../../integrations.js');
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { extractCoverGeniusPolicyIds, processInsuranceData } = require('../../../../functions.js');
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const moment = require('moment');

    console.log(`Processing PNR: ${pnr}`);
    console.log(`Debug: API route working directory: ${process.cwd()}`);

    // Step 1: Retrieve PNR data from FlyDubai API
    console.log('Step 1: Retrieving PNR data from FlyDubai API...');
    const pnrData = await retrieveAndSavePNRData(pnr);
    console.log('✓ PNR data retrieved successfully');

    // Step 2: Extract Cover Genius policy IDs
    console.log('Step 2: Extracting Cover Genius policy IDs...');
    const policyIds = extractCoverGeniusPolicyIds(pnrData);

    if (policyIds.length === 0) {
      console.log('❌ No Cover Genius policy IDs found in PNR data');
      return {
        pnrNumber: pnr,
        policyId: null,
        insuranceRecords: [],
        policyStartDate: null,
        policyEndDate: null,
        summary: {
          totalRecords: 0,
          withConfirmation: 0,
          missingConfirmation: 0,
          withinPolicyPeriod: 0,
        },
        sqlQueries: [],
        error: 'No Cover Genius policy IDs found in PNR data'
      };
    }

    console.log(`✓ Found ${policyIds.length} policy ID(s): ${policyIds.join(', ')}`);

    // Step 3: Process all policy IDs
    console.log(`Step 3: Processing ${policyIds.length} policy ID(s)...`);

    const allResults = [];
    const allInsuranceRecords = [];
    const allSqlQueries = [];
    const allManualRequests = [];
    const allErrors = [];
    let earliestStartDate = null;
    let latestEndDate = null;

    // Process each policy ID
    for (let i = 0; i < policyIds.length; i++) {
      const policyId = policyIds[i];
      console.log(`Step 3.${i + 1}: Processing policy ID: ${policyId}`);

      try {
        // Fetch Cover Genius policy data
        console.log(`  - Fetching policy details from Cover Genius API...`);
        let policyResponse;
        try {
          policyResponse = await fetchCoverGeniusPolicy(policyId);
          console.log(`  ✓ Policy data retrieved successfully`);
        } catch (coverGeniusError) {
          const errorMessage = coverGeniusError instanceof Error ? coverGeniusError.message : 'Unknown Cover Genius API error';
          console.error(`  ❌ Cover Genius API call failed for policy ${policyId}:`, errorMessage);

          // Check if this is a 404 error and generate manual policy request
          const is404Error = errorMessage.includes('404') || errorMessage.includes('Not Found') ||
                           errorMessage.includes('Quote package does not exist');

          let manualRequest = null;
          if (is404Error) {
            console.log(`  📋 Generating manual policy creation request for ${policyId}...`);

            try {
              manualRequest = generateManualPolicyRequest(pnr, policyId, pnrData);
              if (manualRequest) {
                console.log(`  ✓ Manual policy request generated successfully`);
                console.log(`  📄 Request saved to: ManualPolicyRequest-${policyId}-*.json`);
                console.log(`  📋 Curl command ready for manual execution`);
                allManualRequests.push({
                  policyId,
                  manualRequest
                });
              }
            } catch (manualRequestError) {
              console.error(`  ⚠️  Failed to generate manual request: ${manualRequestError instanceof Error ? manualRequestError.message : 'Unknown error'}`);
            }
          }

          // Store error for this policy
          allErrors.push({
            policyId,
            error: `Cover Genius API failed: ${errorMessage}`,
            errorType: is404Error ? 'COVER_GENIUS_404_ERROR' : 'COVER_GENIUS_API_ERROR',
            manualRequest
          });

          // Even though the policy API failed, we should still process the insurance records
          // from the PNR data and mark them with error status
          console.log(`  - Processing insurance records with error status for failed policy...`);
          const processedData = processInsuranceData(pnrData, null, policyId);
          console.log(`  ✓ Processed ${processedData.insuranceRecords.length} total insurance records`);

          // Filter to only include records that belong to this specific failed policy
          const recordsForThisPolicy = processedData.insuranceRecords.filter((record: any) =>
            record.matchesCoverGeniusPolicyId === true
          );

          // Mark only the records that belong to this failed policy with error information
          const errorRecords = recordsForThisPolicy.map((record: any) => ({
            ...record,
            hasError: true,
            errorMessage: `Policy API failed: ${errorMessage}`,
            errorType: is404Error ? 'COVER_GENIUS_404_ERROR' : 'COVER_GENIUS_API_ERROR',
            policyId: policyId
          }));

          // Add error records to the combined list
          allInsuranceRecords.push(...errorRecords);

          console.log(`  ⚠️  Policy ${policyId} failed but ${errorRecords.length} records (out of ${processedData.insuranceRecords.length} total) processed with error status`);
          continue;
        }

        // Extract policy dates
        let policyStartDate = null;
        let policyEndDate = null;
        if (policyResponse && policyResponse.data) {
          if (policyResponse.data.quotes && policyResponse.data.quotes.length > 0) {
            const quote = policyResponse.data.quotes[0];
            if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
            if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
          } else if (policyResponse.data.policy) {
            const policy = policyResponse.data.policy;
            if (policy.start_date) policyStartDate = moment(policy.start_date);
            if (policy.end_date) policyEndDate = moment(policy.end_date);
          }
        } else if (policyResponse && policyResponse.quotes && policyResponse.quotes.length > 0) {
          const quote = policyResponse.quotes[0];
          if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
          if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
        }

        if (policyStartDate && policyEndDate) {
          console.log(`  - Policy period: ${policyStartDate.format("YYYY-MM-DD HH:mm:ss")} to ${policyEndDate.format("YYYY-MM-DD HH:mm:ss")}`);

          // Track earliest start date and latest end date across all policies
          if (!earliestStartDate || policyStartDate.isBefore(earliestStartDate)) {
            earliestStartDate = policyStartDate;
          }
          if (!latestEndDate || policyEndDate.isAfter(latestEndDate)) {
            latestEndDate = policyEndDate;
          }
        }

        // Process the insurance data
        console.log(`  - Processing insurance data...`);
        const processedData = processInsuranceData(pnrData, policyResponse, policyId);
        console.log(`  ✓ Processed ${processedData.insuranceRecords.length} total insurance records`);

        // Filter to only include records that belong to this specific policy
        const recordsForThisPolicy = processedData.insuranceRecords.filter((record: any) =>
          record.matchesCoverGeniusPolicyId === true
        );

        // Add only the records that belong to this policy to the combined list
        allInsuranceRecords.push(...recordsForThisPolicy);

        console.log(`  ✓ Added ${recordsForThisPolicy.length} records (out of ${processedData.insuranceRecords.length} total) for policy ${policyId}`);

        // Calculate summary statistics for this policy (using only records that belong to this policy)
        const missingConfirmations = recordsForThisPolicy.filter((record: any) => !record.hasConfirmation);
        const recordsWithinPolicyPeriod = recordsForThisPolicy.filter((r: any) => r.withinPolicyPeriod === true);

        console.log(`  - Records missing confirmation: ${missingConfirmations.length}`);
        console.log(`  - Records matching policy ID: ${recordsForThisPolicy.length}`);
        console.log(`  - Records within policy period: ${recordsWithinPolicyPeriod.length}`);

        // Generate SQL queries for missing confirmations for this policy
        const policyQueries = missingConfirmations.map((record: any) => ({
          policyId,
          recordNumber: record.recordNumber,
          passenger: record.passengerName,
          query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${policyId}' WHERE CONFIRMATION_NUM='${pnr}' AND RECORD_NUM=${record.recordNumber};`
        }));
        allSqlQueries.push(...policyQueries);

        // Store successful result for this policy
        allResults.push({
          policyId,
          insuranceRecords: recordsForThisPolicy,
          policyStartDate: policyStartDate ? policyStartDate.format('YYYY-MM-DD') : null,
          policyEndDate: policyEndDate ? policyEndDate.format('YYYY-MM-DD') : null,
          summary: {
            totalRecords: recordsForThisPolicy.length,
            withConfirmation: recordsForThisPolicy.length - missingConfirmations.length,
            missingConfirmation: missingConfirmations.length,
            withinPolicyPeriod: recordsWithinPolicyPeriod.length,
          },
          sqlQueries: policyQueries
        });

        console.log(`  ✓ Policy ${policyId} processed successfully`);

      } catch (error) {
        console.error(`  ❌ Error processing policy ${policyId}:`, error);
        allErrors.push({
          policyId,
          error: `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          errorType: 'PROCESSING_ERROR'
        });
        console.log(`  ⚠️  Skipping policy ${policyId} due to processing error, continuing with next policy...`);
      }
    }

    // Calculate combined summary statistics
    const totalMissingConfirmations = allInsuranceRecords.filter((record: any) => !record.hasConfirmation);
    const totalRecordsWithinPolicyPeriod = allInsuranceRecords.filter((r: any) => r.withinPolicyPeriod === true);

    console.log(`\n✓ Processed ${allResults.length} policies successfully, ${allErrors.length} failed`);
    console.log(`✓ Total insurance records across all policies: ${allInsuranceRecords.length}`);
    console.log(`✓ Total SQL queries generated: ${allSqlQueries.length}`);

    // Return structured data with all policies
    const response: any = {
      pnrNumber: pnr,
      policyIds: policyIds,
      totalPolicies: policyIds.length,
      successfulPolicies: allResults.length,
      failedPolicies: allErrors.length,
      insuranceRecords: allInsuranceRecords,
      policyStartDate: earliestStartDate ? earliestStartDate.format('YYYY-MM-DD') : null,
      policyEndDate: latestEndDate ? latestEndDate.format('YYYY-MM-DD') : null,
      summary: {
        totalRecords: allInsuranceRecords.length,
        withConfirmation: allInsuranceRecords.length - totalMissingConfirmations.length,
        missingConfirmation: totalMissingConfirmations.length,
        withinPolicyPeriod: totalRecordsWithinPolicyPeriod.length,
      },
      sqlQueries: allSqlQueries,
      policyResults: allResults
    };

    // Include error details if any policies failed
    if (allErrors.length > 0) {
      response.errors = allErrors;
    }

    // Include manual requests if any were generated
    if (allManualRequests.length > 0) {
      response.manualPolicyRequests = allManualRequests;
    }

    // For backward compatibility, include the first policy ID as policyId
    if (policyIds.length > 0) {
      response.policyId = policyIds[0];
    }

    return response;

  } catch (error) {
    console.error('Error in processMainFunction:', error);
    console.error('Current working directory:', process.cwd());
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { pnr } = await request.json();

    if (!pnr || typeof pnr !== 'string') {
      return NextResponse.json(
        { error: 'PNR is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate PNR format
    const cleanPnr = pnr.trim().toUpperCase();
    if (cleanPnr.length < 3 || cleanPnr.length > 10) {
      return NextResponse.json(
        { error: 'PNR must be between 3 and 10 characters' },
        { status: 400 }
      );
    }

    try {
      const result = await processMainFunction(cleanPnr);
      return NextResponse.json(result);
    } catch (processingError) {
      console.error('Error processing PNR:', processingError);

      return NextResponse.json(
        {
          error: 'Failed to process PNR',
          details: processingError instanceof Error ? processingError.message : 'Unknown processing error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
