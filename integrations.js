// Load environment variables
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { airportToCountry } = require('./constants');

/**
 * Fetches policy details from Cover Genius API using a policy ID
 * @param {string} policyId - The policy ID to retrieve (format: XXXXX-XXXXX-XXX)
 * @returns {Promise} - Promise resolving to policy data
 */
function fetchCoverGeniusPolicy(policyId) {
  const CryptoJS = require('crypto-js');
  const axios = require('axios');

  // Get credentials and configuration from environment variables
  const credentials = {
    api_key: process.env.COVER_GENIUS_API_KEY,
    api_secret: process.env.COVER_GENIUS_API_SECRET
  };
  const partnerId = process.env.COVER_GENIUS_PARTNER_ID;
  const baseUrl = process.env.COVER_GENIUS_API_BASE_URL;

  // Validate environment variables
  if (!credentials.api_key || !credentials.api_secret || !partnerId || !baseUrl) {
    throw new Error('Missing required Cover Genius environment variables. Please check your .env file.');
  }

  // Generate authentication headers
  const date = new Date().toUTCString();
  const signatureContentString = 'date: ' + date;
  const signatureString = CryptoJS.HmacSHA1(signatureContentString, credentials.api_secret)
    .toString(CryptoJS.enc.Base64);
  const authHeader = 'Signature keyId="' + credentials.api_key +
    '",algorithm="hmac-sha1",signature="' +
    encodeURIComponent(signatureString) + '"';

  // Set request options
  const options = {
    method: 'GET',
    url: `${baseUrl}/${partnerId}/bookings/${policyId}`,
    headers: {
      'X-Api-Key': credentials.api_key,
      'Authorization': authHeader,
      'Content-Type': 'application/json',
      'Date': date
    },
    httpsAgent: new (require('https').Agent)({
      rejectUnauthorized: false
    })
  };

  // Make the request and save response to file
  return axios(options).then(response => {
    try {
      // Ensure data directory exists - use robust path resolution
      let dataDir;
      if (process.cwd().includes('ui-app')) {
        // If we're already in ui-app directory
        dataDir = path.join(process.cwd(), 'data');
      } else {
        // If we're in the parent directory
        dataDir = path.join(process.cwd(), 'ui-app', 'data');
      }
      console.log(`Debug: Attempting to save Cover Genius data to: ${dataDir}`);

      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
        console.log(`Debug: Created data directory: ${dataDir}`);
      }

      // Save the Cover Genius response to a file with policy ID in filename
      const filename = `CoverGenius-${policyId}.json`;
      const filePath = path.join(dataDir, filename);
      fs.writeFileSync(filePath, JSON.stringify(response.data, null, 2));
      console.log(`✓ Saved Cover Genius policy data to ${filePath}`);
    } catch (saveError) {
      console.warn(`Warning: Could not save Cover Genius response to file: ${saveError.message}`);
      console.warn(`Debug: __dirname = ${__dirname}`);
      console.warn(`Debug: process.cwd() = ${process.cwd()}`);
    }

    return response;
  }).catch(error => {
    // Enhanced error logging for Cover Genius API failures
    console.error(`❌ Cover Genius API call failed for policy ID: ${policyId}`);
    console.error('Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method
    });

    // Log the full error response if available
    if (error.response?.data) {
      console.error('Cover Genius API error response:', JSON.stringify(error.response.data, null, 2));

      // Save error response to file for debugging
      try {
        let dataDir;
        if (process.cwd().includes('ui-app')) {
          // If we're already in ui-app directory
          dataDir = path.join(process.cwd(), 'data');
        } else {
          // If we're in the parent directory
          dataDir = path.join(process.cwd(), 'ui-app', 'data');
        }
        console.log(`Debug: Attempting to save Cover Genius error to: ${dataDir}`);

        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
          console.log(`Debug: Created data directory for error: ${dataDir}`);
        }

        const errorFilename = `CoverGenius-ERROR-${policyId}-${Date.now()}.json`;
        const errorFilePath = path.join(dataDir, errorFilename);
        fs.writeFileSync(errorFilePath, JSON.stringify({
          policyId,
          timestamp: new Date().toISOString(),
          error: {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        }, null, 2));
        console.log(`✓ Saved Cover Genius error response to ${errorFilePath}`);
      } catch (saveError) {
        console.warn(`Warning: Could not save Cover Genius error response to file: ${saveError.message}`);
        console.warn(`Debug: __dirname = ${__dirname}`);
        console.warn(`Debug: process.cwd() = ${process.cwd()}`);
      }
    }

    // Re-throw the error to ensure the transaction fails
    throw new Error(`Cover Genius API failed for policy ${policyId}: ${error.message}${error.response?.status ? ` (HTTP ${error.response.status})` : ''}`);
  });
}

/**
 * Generates a manual policy creation request for Cover Genius when API returns 404
 * @param {string} pnr - The PNR number
 * @param {string} policyId - The policy ID that failed
 * @param {Object} pnrData - The PNR data object
 * @returns {Object} - Object containing the curl command and request details
 */
function generateManualPolicyRequest(pnr, policyId, pnrData) {
  try {
    // Extract passenger information from PNR data
    const passengers = pnrData.persons || [];
    const insuredPersons = [];
    let primaryPassenger = null;

    // Process each passenger
    passengers.forEach(person => {
      const insuredPerson = {
        first_name: person.fName || "PASSENGER",
        last_name: person.lName || "NAME"
      };
      insuredPersons.push(insuredPerson);

      // Set the first passenger as primary (policyholder)
      if (!primaryPassenger) {
        primaryPassenger = insuredPerson;
      }
    });

    // If no passengers found, create default entries
    if (insuredPersons.length === 0) {
      insuredPersons.push(
        { first_name: "PASSENGER", last_name: "ONE" },
        { first_name: "PASSENGER", last_name: "TWO" }
      );
      primaryPassenger = insuredPersons[0];
    }

    // Extract quote ID from insurance transaction ID (second part after slash)
    let quoteId = policyId.replace('-INS', ''); // Default fallback

    // Look for the insurance transaction ID in paxSegments to get the correct quote ID
    if (pnrData.paxSegments && pnrData.paxSegments.length > 0) {
      for (const paxSegment of pnrData.paxSegments) {
        if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
          for (const recordDetail of paxSegment.recordDetails) {
            if (recordDetail.insuTransID && recordDetail.insuTransID.includes('/')) {
              // Extract the part after the slash as the quote ID
              const parts = recordDetail.insuTransID.split('/');
              if (parts.length > 1 && parts[1].trim()) {
                quoteId = parts[1].trim();
                break;
              }
            }
          }
          if (quoteId !== policyId.replace('-INS', '')) {
            break; // Found the quote ID, exit outer loop
          }
        }
      }
    }

    // Extract contact information from PNR data - specifically primary passenger email
    let contactEmail = "<EMAIL>"; // Default placeholder

    // Find the primary passenger first
    let primaryPassengerId = null;
    if (passengers.length > 0) {
      // Look for primary passenger in paxSegments
      if (pnrData.paxSegments && pnrData.paxSegments.length > 0) {
        for (const paxSegment of pnrData.paxSegments) {
          if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
            // Check all record details, not just the first one
            for (const recordDetail of paxSegment.recordDetails) {
              if (recordDetail.primaryPax === true) {
                // Find the passenger ID for this record
                const passenger = passengers.find(p => p.recNum && p.recNum.includes(paxSegment.recNum));
                if (passenger) {
                  primaryPassengerId = passenger.paxID;
                  break;
                }
              }
            }
            // If we found the primary passenger, break out of outer loop too
            if (primaryPassengerId) {
              break;
            }
          }
        }
      }

      // If no primary passenger found in paxSegments, use the first passenger
      if (!primaryPassengerId && passengers[0].paxID) {
        primaryPassengerId = passengers[0].paxID;
      }
    }

    // Now look for email contact info for the primary passenger
    // Check both 'contactInfos' and 'contacts' arrays as the structure may vary
    const contactsArray = pnrData.contactInfos || pnrData.contacts || [];

    if (contactsArray.length > 0 && primaryPassengerId) {
      for (const contact of contactsArray) {
        if (contact.paxID === primaryPassengerId &&
            contact.contactType === 'Email') {
          // Check both 'contactInfo' and 'contactField' as field names may vary
          const emailField = contact.contactInfo || contact.contactField;
          if (emailField && emailField.includes('@')) {
            contactEmail = emailField;
            break;
          }
        }
      }
    }

    // Determine country code from first segment's origin (sorted by departure date)
    let countryCode = "AE"; // Default to UAE
    let firstSegmentOrigin = 'N/A';

    if (pnrData.segments && pnrData.segments.length > 0) {
      // Sort segments by departure date in ascending order to get the earliest departure
      const sortedSegments = [...pnrData.segments].sort((a, b) => {
        // Handle cases where depDate might be missing or invalid
        const dateA = a.depDate ? new Date(a.depDate) : new Date('9999-12-31');
        const dateB = b.depDate ? new Date(b.depDate) : new Date('9999-12-31');
        return dateA.getTime() - dateB.getTime();
      });

      const firstSegment = sortedSegments[0];
      if (firstSegment.org) {
        firstSegmentOrigin = firstSegment.org;
        // Use airport to country mapping from constants
        countryCode = airportToCountry[firstSegment.org] || "AE";
      }
    }

    // Create the request payload
    const requestPayload = {
      quotes: [
        {
          id: quoteId,
          insured: insuredPersons
        }
      ],
      policyholder: {
        first_name: primaryPassenger.first_name,
        last_name: primaryPassenger.last_name,
        email: contactEmail,
        country: countryCode
      }
    };

    // Get environment variables for the request
    const partnerId = process.env.COVER_GENIUS_PARTNER_ID || "{{PNR}}";
    const baseUrl = process.env.COVER_GENIUS_API_BASE_URL || "https://test.covergenius.com/x/partners";

    // Generate the curl command
    const curlCommand = `curl --location '${baseUrl}/${partnerId}/bookings/${policyId}' \\
--header 'Content-Type: application/json' \\
--data-raw '${JSON.stringify(requestPayload, null, 2)}'`;

    // Create the manual request object
    const manualRequest = {
      pnr: pnr,
      policyId: policyId,
      timestamp: new Date().toISOString(),
      requestDetails: {
        url: `${baseUrl}/${partnerId}/bookings/${policyId}`,
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        payload: requestPayload
      },
      curlCommand: curlCommand,
      instructions: [
        "1. Verify the email address in the policyholder section is correct",
        "2. Verify passenger names are correct",
        "3. Confirm the country code is appropriate",
        "4. Execute the curl command manually",
        "5. Save the response for record keeping"
      ],
      notes: {
        extractedPassengers: passengers.length,
        extractedEmail: contactEmail,
        primaryPassengerId: primaryPassengerId,
        firstSegmentOrigin: firstSegmentOrigin,
        detectedCountry: countryCode,
        quoteId: quoteId,
        quoteIdSource: quoteId !== policyId.replace('-INS', '') ? 'insuTransID' : 'policyId',
        hasContactInfo: !!((pnrData.contactInfos && pnrData.contactInfos.length > 0) || (pnrData.contacts && pnrData.contacts.length > 0)),
        emailFromPrimaryPax: contactEmail !== "<EMAIL>",
        segmentsSortedByDepartureDate: true
      }
    };

    // Save the manual request to a file
    let dataDir;
    if (process.cwd().includes('ui-app')) {
      dataDir = path.join(process.cwd(), 'data');
    } else {
      dataDir = path.join(process.cwd(), 'ui-app', 'data');
    }

    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const requestFilename = `ManualPolicyRequest-${policyId}-${Date.now()}.json`;
    const requestFilePath = path.join(dataDir, requestFilename);

    try {
      fs.writeFileSync(requestFilePath, JSON.stringify(manualRequest, null, 2));
      console.log(`📋 Manual policy request saved to: ${requestFilename}`);
    } catch (writeError) {
      console.error('Failed to save manual request:', writeError.message);
    }

    return manualRequest;

  } catch (error) {
    console.error('Error generating manual policy request:', error.message);
    return null;
  }
}

/**
 * Fetches a security token from the FlyDubai API
 * @returns {Promise<string>} - Promise resolving to the security token
 */
async function getSecurityToken() {
    try {
        // Get credentials from environment variables
        const baseUrl = process.env.FZ_API_BASE_URL;
        const username = process.env.FZ_USERNAME;
        const password = process.env.FZ_PASSWORD;

        // Validate environment variables
        if (!baseUrl || !username || !password) {
            throw new Error('Missing required FlyDubai API environment variables. Please check your .env file.');
        }

        const response = await axios.post(`${baseUrl}/security/token`, {
            userName: username,
            password: password
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            })
        });

        if (response.data && response.data.token) {
            return response.data.token;
        } else {
            throw new Error('Token not found in response');
        }
    } catch (error) {
        console.error('Error fetching security token:', error.message);
        throw error;
    }
}

/**
 * Fetches PNR data from the FlyDubai API
 * @param {string} pnrNumber - The PNR number to fetch
 * @param {string} token - The security token for authentication
 * @returns {Promise<object>} - Promise resolving to the PNR data
 */
async function fetchPNRData(pnrNumber, token) {
    try {
        const baseUrl = process.env.FZ_API_BASE_URL;

        if (!baseUrl) {
            throw new Error('Missing FZ_API_BASE_URL environment variable');
        }

        const response = await axios.post(`${baseUrl}/reservations/${pnrNumber}`, {
            transactionDetails: {
                clientIPAddress: "127.0.0.1",
                userName: ""
            },
            filterDetails: {
                DisplayReservation: true,
                DisplayPayments: true,
                DisplayOAFlights: false,
                DisplayComments: false,
                DisplayHistory: false,
                DisplaySegments: true,
                DisplayPersons: true,
                DisplaySegmentPersonMap: true,
                DisplaySeatAssignment: false,
                DisplayAPISInfo: false,
                DisplayPriceInfo: false,
                DisplayCharges: true,
                DisplayResPaymentMap: true,
                DisplayPhysicalFlights: true,
                DisplayReservationContacts: false,
                DisplayContactInfos: true
            }
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            })
        });

        return response.data;
    } catch (error) {
        console.error('Error fetching PNR data:', error.message);
        throw error;
    }
}

/**
 * Retrieves PNR data and saves it to files
 * @param {string} pnrNumber - The PNR number to retrieve
 * @returns {Promise<object>} - Promise resolving to the PNR data
 */
async function retrieveAndSavePNRData(pnrNumber) {
    try {
        console.log(`Fetching data for PNR: ${pnrNumber}`);

        // Get security token
        const token = await getSecurityToken();
        console.log('Security token obtained successfully');

        // Fetch PNR data
        const retrievePNRData = await fetchPNRData(pnrNumber, token);
        console.log('PNR data fetched successfully');

        // Ensure data directory exists - use robust path resolution
        let dataDir;
        if (process.cwd().includes('ui-app')) {
          // If we're already in ui-app directory
          dataDir = path.join(process.cwd(), 'data');
        } else {
          // If we're in the parent directory
          dataDir = path.join(process.cwd(), 'ui-app', 'data');
        }
        console.log(`Debug: Attempting to save PNR data to: ${dataDir}`);

        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
            console.log(`Debug: Created data directory: ${dataDir}`);
        }

        // Save the PNR data to a file for reference with PNR number in filename
        const pnrFileName = `RetrievePNR-${pnrNumber}.json`;
        const pnrFilePath = path.join(dataDir, pnrFileName);
        fs.writeFileSync(pnrFilePath, JSON.stringify(retrievePNRData, null, 2));
        console.log(`✓ Saved PNR data to ${pnrFilePath}`);
        console.log(`  - File size: ${(JSON.stringify(retrievePNRData).length / 1024).toFixed(2)} KB`);

        return retrievePNRData;
    } catch (error) {
        console.error('❌ Error retrieving PNR data:', error.message);

        // Log additional error details for debugging
        if (error.response) {
            console.error('  - HTTP Status:', error.response.status);
            console.error('  - Response data:', error.response.data);
        }

        // Debug path information
        console.warn(`Debug: __dirname = ${__dirname}`);
        console.warn(`Debug: process.cwd() = ${process.cwd()}`);

        throw error;
    }
}

// Export all functions for use in other modules
module.exports = {
    fetchCoverGeniusPolicy,
    generateManualPolicyRequest,
    getSecurityToken,
    fetchPNRData,
    retrieveAndSavePNRData
};

